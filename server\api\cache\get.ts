export default defineEventHandler(async (event) => {
	// get all cache keys
	try {
		const redis = createStorage({
			driver: redisDriver({
				tls: {
					host: 'v2-8e6rqo.serverless.euw1.cache.amazonaws.com',
					port: 6379,
				},
			}),
		})

		const keys = await redis.getKeys('*')

		return {
			message: 'Cache keys',
			keys,
		}
	} catch (error) {
		return {
			message: 'Failed to get cache keys',
			error,
		}
	}
})
